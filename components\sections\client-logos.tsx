"use client"

import { useEffect, useRef, useState } from "react"
import Image from "next/image"

const clients = [
  { name: "Godrej Properties", logo: "/images/clients/godrej.webp" },
  { name: "Sobha Limited", logo: "/images/clients/sobha.webp" },
  { name: "Lodha Group", logo: "/images/clients/lodha.webp" },
  { name: "Aaryan Group", logo: "/images/clients/aaryan.webp" },
  { name: "Aaryan Properties", logo: "/images/clients/aaryan-2.webp" },
  { name: "Gala Group", logo: "/images/clients/gald.webp" },
  { name: "HRG Group", logo: "/images/clients/hrg.webp" },
  { name: "Safal Group", logo: "/images/clients/safal.webp" },
  { name: "Savaliya Group", logo: "/images/clients/savaliya.webp" },
  { name: "Shaligram Group", logo: "/images/clients/shaligram.webp" },
  { name: "Shipl Group", logo: "/images/clients/shipl.webp" },
  { name: "Swagat Group", logo: "/images/clients/swagat.webp" },
  { name: "Swati Group", logo: "/images/clients/swati.webp" },
  { name: "Arista Homes", logo: "/images/clients/arista.webp" },
  { name: "Constera Group", logo: "/images/clients/constera.webp" },
  { name: "Deep Group", logo: "/images/clients/deep.webp" },
  { name: "E Square", logo: "/images/clients/e.webp" },
  { name: "HR Group", logo: "/images/clients/hr.webp" },
  { name: "Pacific Group", logo: "/images/clients/pacific.webp" },
  { name: "PD Group", logo: "/images/clients/pd.webp" },
  { name: "Ratnanjali Group", logo: "/images/clients/ratnanjali.webp" },
  { name: "Venus Group", logo: "/images/clients/venus.webp" },
  { name: "Zaveri Group", logo: "/images/clients/zaveri.webp" },
]

export default function ClientLogos() {
  const sectionRef = useRef<HTMLElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
            setIsVisible(true)
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 animate-on-scroll opacity-0 translate-y-8">
          <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
            Trusted by Leading{" "}
            <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
              Developers
            </span>
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            We partner with India's most reputed real estate developers to bring you premium properties and investment
            opportunities.
          </p>
        </div>

        <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
          {/* Scrolling Logo Animation */}
          <div className="relative overflow-hidden group logo-slider-container">
            {/* Gradient fade edges */}
            <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-gray-50 to-transparent z-10 pointer-events-none"></div>
            <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-gray-50 to-transparent z-10 pointer-events-none"></div>

            <div className={`flex space-x-16 items-center min-w-max ${isVisible ? 'animate-scroll' : ''} group-hover:animate-pause`}>
              {/* First set of logos */}
              {clients.map((client, index) => (
                <div
                  key={`first-${index}`}
                  className="flex-shrink-0 transition-all duration-300 opacity-60 hover:opacity-100 hover:scale-110"
                >
                  <Image
                    src={client.logo}
                    alt={client.name}
                    width={140}
                    height={70}
                    className="h-16 w-auto object-contain grayscale hover:grayscale-0"
                    loading="lazy"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/placeholder.svg";
                    }}
                  />
                </div>
              ))}
              {/* Duplicate set for seamless loop */}
              {clients.map((client, index) => (
                <div
                  key={`second-${index}`}
                  className="flex-shrink-0 transition-all duration-300 opacity-60 hover:opacity-100 hover:scale-110"
                >
                  <Image
                    src={client.logo}
                    alt={client.name}
                    width={140}
                    height={70}
                    className="h-16 w-auto object-contain grayscale hover:grayscale-0"
                    loading="lazy"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/placeholder.svg";
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
